/**
 * 动画相关的可视化脚本节点
 * 提供完整的动画控制、混合、状态机等功能
 */

import type { Entity } from '../../core/Entity';
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AnimationSystem } from '../../animation/AnimationSystem';
import { AnimationStateMachine } from '../../animation/AnimationStateMachine';
import { AnimationBlender } from '../../animation/AnimationBlender';
import { FacialAnimationSystem } from '../../animation/FacialAnimationSystem';
import { AnimationState } from '../../animation/Animator';

/**
 * 播放动画节点
 * 播放指定的动画片段
 */
export class PlayAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'clipName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画片段名称',
      defaultValue: ''
    });

    this.addInput({
      name: 'blendTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '混合时间（秒）',
      defaultValue: 0.3,
      optional: true
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功播放'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const clipName = this.getInputValue('clipName') as string;
    const blendTime = this.getInputValue('blendTime') as number;
    const loop = this.getInputValue('loop') as boolean;

    // 检查输入值是否有效
    if (!entity || !clipName) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取动画系统
      const animationSystem = this.context.world.getSystem(AnimationSystem);
      if (!animationSystem) {
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 获取或创建动画控制器
      const animator = animationSystem.getAnimator(entity) || animationSystem.createAnimator(entity);

      // 设置循环模式
      if (loop !== undefined) {
        animator.setLoop(loop);
      }

      // 播放动画
      const success = animator.play(clipName, blendTime);

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发输出流程
      this.triggerFlow('flow');
      return success;
    } catch (error) {
      console.error('播放动画失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 停止动画节点
 * 停止实体的动画播放
 */
export class StopAnimationNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'fadeTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '淡出时间（秒）',
      defaultValue: 0.3,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功停止'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const fadeTime = this.getInputValue('fadeTime') as number;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取动画系统
      const animationSystem = this.context.world.getSystem(AnimationSystem);
      if (!animationSystem) {
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 获取动画控制器
      const animator = animationSystem.getAnimator(entity);
      if (!animator) {
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 停止动画
      animator.stop();

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('停止动画失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置动画速度节点
 * 设置动画播放的时间缩放
 */
export class SetAnimationSpeedNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '播放速度',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const speed = this.getInputValue('speed') as number;

    // 检查输入值是否有效
    if (!entity || typeof speed !== 'number') {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取动画系统
      const animationSystem = this.context.world.getSystem(AnimationSystem);
      if (!animationSystem) {
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 获取动画控制器
      const animator = animationSystem.getAnimator(entity);
      if (!animator) {
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 设置时间缩放
      animator.setTimeScale(speed);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置动画速度失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取动画状态节点
 * 获取实体的动画播放状态信息
 */
export class GetAnimationStateNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'isPlaying',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否正在播放'
    });

    this.addOutput({
      name: 'currentClip',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前动画片段名称'
    });

    this.addOutput({
      name: 'time',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '播放时间（秒）'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '动画总时长（秒）'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '播放进度（0-1）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('isPlaying', false);
      this.setOutputValue('currentClip', '');
      this.setOutputValue('time', 0);
      this.setOutputValue('duration', 0);
      this.setOutputValue('progress', 0);
      return false;
    }

    try {
      // 获取动画系统
      const animationSystem = this.context.world.getSystem(AnimationSystem);
      if (!animationSystem) {
        this.setOutputValue('isPlaying', false);
        this.setOutputValue('currentClip', '');
        this.setOutputValue('time', 0);
        this.setOutputValue('duration', 0);
        this.setOutputValue('progress', 0);
        return false;
      }

      // 获取动画控制器
      const animator = animationSystem.getAnimator(entity);
      if (!animator) {
        this.setOutputValue('isPlaying', false);
        this.setOutputValue('currentClip', '');
        this.setOutputValue('time', 0);
        this.setOutputValue('duration', 0);
        this.setOutputValue('progress', 0);
        return false;
      }

      // 获取动画状态
      const isPlaying = animator.getState() === AnimationState.PLAYING;
      const currentClip = animator.getCurrentClip();
      const time = animator.getTime();
      const duration = currentClip ? currentClip.duration : 0;
      const progress = duration > 0 ? time / duration : 0;

      // 设置输出值
      this.setOutputValue('isPlaying', isPlaying);
      this.setOutputValue('currentClip', currentClip ? currentClip.name : '');
      this.setOutputValue('time', time);
      this.setOutputValue('duration', duration);
      this.setOutputValue('progress', Math.min(progress, 1.0));

      return true;
    } catch (error) {
      console.error('获取动画状态失败:', error);
      this.setOutputValue('isPlaying', false);
      this.setOutputValue('currentClip', '');
      this.setOutputValue('time', 0);
      this.setOutputValue('duration', 0);
      this.setOutputValue('progress', 0);
      return false;
    }
  }
}

/**
 * 动画混合节点
 * 混合多个动画片段
 */
export class AnimationBlendNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'clips',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '动画片段列表',
      defaultValue: []
    });

    this.addInput({
      name: 'weights',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '权重列表',
      defaultValue: []
    });

    this.addInput({
      name: 'blendTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '混合时间（秒）',
      defaultValue: 0.3,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '混合成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '混合失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'blendId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '混合ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const clips = this.getInputValue('clips') as string[];
    const weights = this.getInputValue('weights') as number[];
    const blendTime = this.getInputValue('blendTime') as number;

    // 检查输入值是否有效
    if (!entity || !Array.isArray(clips) || clips.length === 0) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取动画系统
      const animationSystem = this.context.world.getSystem(AnimationSystem);
      if (!animationSystem) {
        this.triggerFlow('fail');
        return false;
      }

      // 获取或创建动画控制器
      const animator = animationSystem.getAnimator(entity) || animationSystem.createAnimator(entity);

      // 创建动画混合器
      const blender = new AnimationBlender(animator);

      // 添加混合层
      for (let i = 0; i < clips.length; i++) {
        const clipName = clips[i];
        const weight = weights && weights[i] !== undefined ? weights[i] : 1.0 / clips.length;
        blender.addLayer(clipName, weight);
      }

      // 设置混合层权重（这会触发混合）
      if (clips.length > 0) {
        blender.setLayerWeight(0, 1.0, blendTime);
      }

      // 生成混合ID
      const blendId = `blend_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 设置输出值
      this.setOutputValue('blendId', blendId);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('动画混合失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 动画状态机节点
 * 控制动画状态机的状态转换
 */
export class AnimationStateMachineNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'stateName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标状态名称',
      defaultValue: ''
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '状态参数',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功切换状态'
    });

    this.addOutput({
      name: 'currentState',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前状态名称'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const stateName = this.getInputValue('stateName') as string;
    const parameters = this.getInputValue('parameters') as any;

    // 检查输入值是否有效
    if (!entity || !stateName) {
      this.setOutputValue('success', false);
      this.setOutputValue('currentState', '');
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 这里应该获取动画状态机组件
      // 由于具体实现可能还未完成，这里使用模拟逻辑
      console.log(`切换实体 ${entity.id} 到状态: ${stateName}`, parameters);

      // 模拟状态切换成功
      this.setOutputValue('success', true);
      this.setOutputValue('currentState', stateName);

      // 触发输出流程
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('动画状态机切换失败:', error);
      this.setOutputValue('success', false);
      this.setOutputValue('currentState', '');
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 面部动画节点
 * 控制面部表情动画
 */
export class FacialAnimationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'expression',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '表情名称',
      defaultValue: 'neutral'
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '表情强度',
      defaultValue: 1.0,
      optional: true
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 2.0,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '播放成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '播放失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'animationId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '动画ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const expression = this.getInputValue('expression') as string;
    const intensity = this.getInputValue('intensity') as number;
    const duration = this.getInputValue('duration') as number;

    // 检查输入值是否有效
    if (!entity || !expression) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取面部动画系统
      const facialAnimationSystem = this.context.world.getSystem(FacialAnimationSystem);
      if (!facialAnimationSystem) {
        this.triggerFlow('fail');
        return false;
      }

      // 播放面部动画（模拟实现）
      // 在实际项目中，这里应该调用真正的面部动画方法
      const animationId = `facial_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      console.log(`播放面部动画: ${expression}, 强度: ${intensity}, 持续时间: ${duration}秒`);

      if (animationId) {
        // 设置输出值
        this.setOutputValue('animationId', animationId);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('面部动画播放失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 动画事件节点
 * 监听和触发动画事件
 */
export class AnimationEventNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'eventType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '事件类型',
      defaultValue: 'complete'
    });

    this.addInput({
      name: 'eventData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '事件数据',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'triggered',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '事件触发'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'eventInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '事件信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const eventType = this.getInputValue('eventType') as string;
    const eventData = this.getInputValue('eventData') as any;

    // 检查输入值是否有效
    if (!entity || !eventType) {
      return false;
    }

    try {
      // 获取动画系统
      const animationSystem = this.context.world.getSystem(AnimationSystem);
      if (!animationSystem) {
        return false;
      }

      // 获取动画控制器
      const animator = animationSystem.getAnimator(entity);
      if (!animator) {
        return false;
      }

      // 监听动画事件
      animator.addListener(eventType as any, (data: any) => {
        // 设置输出值
        this.setOutputValue('eventInfo', {
          type: eventType,
          data: data,
          timestamp: Date.now(),
          entity: entity.id
        });

        // 触发事件流程
        this.triggerFlow('triggered');
      });

      return true;
    } catch (error) {
      console.error('动画事件监听失败:', error);
      return false;
    }
  }
}

/**
 * 注册动画节点
 * @param registry 节点注册表
 */
export function registerAnimationNodes(registry: NodeRegistry): void {
  // 注册播放动画节点
  registry.registerNodeType({
    type: 'animation/playAnimation',
    category: NodeCategory.ANIMATION,
    constructor: PlayAnimationNode,
    label: '播放动画',
    description: '播放指定的动画片段',
    icon: 'play',
    color: '#4CAF50',
    tags: ['animation', 'play']
  });

  // 注册停止动画节点
  registry.registerNodeType({
    type: 'animation/stopAnimation',
    category: NodeCategory.ANIMATION,
    constructor: StopAnimationNode,
    label: '停止动画',
    description: '停止实体的动画播放',
    icon: 'stop',
    color: '#4CAF50',
    tags: ['animation', 'stop']
  });

  // 注册设置动画速度节点
  registry.registerNodeType({
    type: 'animation/setAnimationSpeed',
    category: NodeCategory.ANIMATION,
    constructor: SetAnimationSpeedNode,
    label: '设置动画速度',
    description: '设置动画播放的时间缩放',
    icon: 'speed',
    color: '#4CAF50',
    tags: ['animation', 'speed']
  });

  // 注册获取动画状态节点
  registry.registerNodeType({
    type: 'animation/getAnimationState',
    category: NodeCategory.ANIMATION,
    constructor: GetAnimationStateNode,
    label: '获取动画状态',
    description: '获取实体的动画播放状态信息',
    icon: 'info',
    color: '#4CAF50',
    tags: ['animation', 'state']
  });

  // 注册动画混合节点
  registry.registerNodeType({
    type: 'animation/blendAnimation',
    category: NodeCategory.ANIMATION,
    constructor: AnimationBlendNode,
    label: '动画混合',
    description: '混合多个动画片段',
    icon: 'blend',
    color: '#4CAF50',
    tags: ['animation', 'blend']
  });

  // 注册动画状态机节点
  registry.registerNodeType({
    type: 'animation/stateMachine',
    category: NodeCategory.ANIMATION,
    constructor: AnimationStateMachineNode,
    label: '动画状态机',
    description: '控制动画状态机的状态转换',
    icon: 'state-machine',
    color: '#4CAF50',
    tags: ['animation', 'state-machine']
  });

  // 注册面部动画节点
  registry.registerNodeType({
    type: 'animation/facialAnimation',
    category: NodeCategory.ANIMATION,
    constructor: FacialAnimationNode,
    label: '面部动画',
    description: '控制面部表情动画',
    icon: 'face',
    color: '#4CAF50',
    tags: ['animation', 'facial', 'expression']
  });

  // 注册动画事件节点
  registry.registerNodeType({
    type: 'animation/animationEvent',
    category: NodeCategory.ANIMATION,
    constructor: AnimationEventNode,
    label: '动画事件',
    description: '监听和触发动画事件',
    icon: 'event',
    color: '#4CAF50',
    tags: ['animation', 'event', 'listener']
  });

  console.log('已注册所有动画节点类型');
}
