/**
 * 音频相关的可视化脚本节点
 * 提供完整的音频播放、处理、分析和效果功能
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeRegistry } from '../NodeRegistry';
import { AudioSystem, AudioType } from '../../audio/AudioSystem';

/**
 * 获取音频系统实例
 */
function getAudioSystem(): AudioSystem | null {
  // 这里应该从引擎实例中获取音频系统
  // 暂时返回null，实际使用时需要注入音频系统实例
  return (globalThis as any).audioSystem || null;
}

/**
 * 播放音频节点
 */
export class PlayAudioNode extends VisualScriptNode {
  constructor() {
    super('PlayAudio', '播放音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioClip', 'string', '音频片段');
    this.addInput('volume', 'number', '音量', 1.0);
    this.addInput('loop', 'boolean', '循环', false);
    this.addInput('offset', 'number', '偏移时间', 0);
    this.addInput('duration', 'number', '持续时间');
    this.addInput('audioType', 'string', '音频类型', 'sound');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
    this.addOutput('audioId', 'string', '音频ID');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioClip) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const audioId = `audio_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      const audioType = this.getAudioType(inputs.audioType);

      const options = {
        type: audioType,
        volume: Math.max(0, Math.min(1, inputs.volume || 1.0)),
        loop: inputs.loop || false,
        offset: inputs.offset || 0,
        duration: inputs.duration
      };

      const success = audioSystem.play(audioId, inputs.audioClip, options);

      if (success) {
        return {
          completed: true,
          audioId: audioId
        };
      } else {
        return { failed: true };
      }
    } catch (error) {
      console.error('播放音频失败:', error);
      return { failed: true };
    }
  }

  private getAudioType(typeString: string): AudioType {
    switch (typeString?.toLowerCase()) {
      case 'music': return AudioType.MUSIC;
      case 'voice': return AudioType.VOICE;
      case 'ambient': return AudioType.AMBIENT;
      case 'ui': return AudioType.UI;
      default: return AudioType.SOUND;
    }
  }
}

/**
 * 停止音频节点
 */
export class StopAudioNode extends VisualScriptNode {
  constructor() {
    super('StopAudio', '停止音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('stopAll', 'boolean', '停止全部', false);
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      if (inputs.stopAll) {
        // 停止所有音频
        const playingSources = audioSystem.getPlayingSources();
        playingSources.forEach(source => {
          const sourceId = (source as any).getId?.() || 'unknown';
          audioSystem.stop(sourceId);
        });
        return { completed: true };
      } else if (inputs.audioId) {
        // 停止指定音频
        const success = audioSystem.stop(inputs.audioId);
        return success ? { completed: true } : { failed: true };
      } else {
        return { failed: true };
      }
    } catch (error) {
      console.error('停止音频失败:', error);
      return { failed: true };
    }
  }
}

/**
 * 设置音量节点
 */
export class SetVolumeNode extends VisualScriptNode {
  constructor() {
    super('SetVolume', '设置音量');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('volume', 'number', '音量', 1.0);
    this.addInput('fadeTime', 'number', '淡入淡出时间', 0);
    this.addInput('audioType', 'string', '音频类型');
    this.addInput('global', 'boolean', '全局音量', false);
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || typeof inputs.volume !== 'number') {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const volume = Math.max(0, Math.min(1, inputs.volume));
      const fadeTime = inputs.fadeTime || 0;

      if (inputs.global) {
        // 设置全局音量
        audioSystem.setVolume(volume);
        return { completed: true };
      } else if (inputs.audioType) {
        // 设置音频类型音量
        const audioType = this.getAudioType(inputs.audioType);
        audioSystem.setTypeVolume(audioType, volume);
        return { completed: true };
      } else if (inputs.audioId) {
        // 设置指定音频源音量
        const source = audioSystem.getSource(inputs.audioId);
        if (source) {
          if (fadeTime > 0) {
            // TODO: 实现音量淡入淡出
            source.setVolume(volume);
          } else {
            source.setVolume(volume);
          }
          return { completed: true };
        } else {
          return { failed: true };
        }
      } else {
        return { failed: true };
      }
    } catch (error) {
      console.error('设置音量失败:', error);
      return { failed: true };
    }
  }

  private getAudioType(typeString: string): AudioType {
    switch (typeString?.toLowerCase()) {
      case 'music': return AudioType.MUSIC;
      case 'voice': return AudioType.VOICE;
      case 'ambient': return AudioType.AMBIENT;
      case 'ui': return AudioType.UI;
      default: return AudioType.SOUND;
    }
  }
}

/**
 * 音频分析节点
 */
export class AudioAnalyzerNode extends VisualScriptNode {
  private analyser: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private frequencyArray: Uint8Array | null = null;

  constructor() {
    super('AudioAnalyzer', '音频分析');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('fftSize', 'number', 'FFT大小', 2048);
    this.addInput('smoothingTimeConstant', 'number', '平滑时间常数', 0.8);
    this.addOutput('volume', 'number', '音量');
    this.addOutput('frequency', 'array', '频率数据');
    this.addOutput('waveform', 'array', '波形数据');
    this.addOutput('peak', 'number', '峰值');
    this.addOutput('rms', 'number', 'RMS值');
  }

  public execute(inputs: any): any {
    if (!inputs.audioId) {
      return this.getDefaultOutput();
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      return this.getDefaultOutput();
    }

    try {
      const source = audioSystem.getSource(inputs.audioId);
      if (!source) {
        return this.getDefaultOutput();
      }

      // 获取音频源的分析器节点
      const analyser = this.getOrCreateAnalyser(source, inputs);
      if (!analyser) {
        return this.getDefaultOutput();
      }

      // 获取频率数据
      if (!this.frequencyArray || this.frequencyArray.length !== analyser.frequencyBinCount) {
        this.frequencyArray = new Uint8Array(analyser.frequencyBinCount);
      }
      analyser.getByteFrequencyData(this.frequencyArray);

      // 获取波形数据
      if (!this.dataArray || this.dataArray.length !== analyser.fftSize) {
        this.dataArray = new Uint8Array(analyser.fftSize);
      }
      analyser.getByteTimeDomainData(this.dataArray);

      // 计算音量、峰值和RMS
      const { volume, peak, rms } = this.calculateAudioMetrics(this.dataArray);

      return {
        volume,
        frequency: Array.from(this.frequencyArray),
        waveform: Array.from(this.dataArray),
        peak,
        rms
      };
    } catch (error) {
      console.error('音频分析失败:', error);
      return this.getDefaultOutput();
    }
  }

  private getOrCreateAnalyser(source: any, inputs: any): AnalyserNode | null {
    try {
      // 这里需要从音频源获取分析器节点
      // 由于AudioSource类的具体实现可能不同，这里使用通用方法
      if (source.getAnalyser) {
        return source.getAnalyser();
      }

      // 如果音频源没有分析器，尝试创建一个
      const audioSystem = getAudioSystem();
      if (audioSystem && (audioSystem as any).context) {
        const context = (audioSystem as any).context;
        if (!this.analyser) {
          this.analyser = context.createAnalyser();
          this.analyser.fftSize = inputs.fftSize || 2048;
          this.analyser.smoothingTimeConstant = inputs.smoothingTimeConstant || 0.8;
        }
        return this.analyser;
      }

      return null;
    } catch (error) {
      console.error('创建音频分析器失败:', error);
      return null;
    }
  }

  private calculateAudioMetrics(dataArray: Uint8Array): { volume: number; peak: number; rms: number } {
    let sum = 0;
    let peak = 0;

    for (let i = 0; i < dataArray.length; i++) {
      const value = (dataArray[i] - 128) / 128; // 转换为-1到1的范围
      const absValue = Math.abs(value);

      sum += value * value;
      if (absValue > peak) {
        peak = absValue;
      }
    }

    const rms = Math.sqrt(sum / dataArray.length);
    const volume = rms; // 简化的音量计算

    return { volume, peak, rms };
  }

  private getDefaultOutput(): any {
    return {
      volume: 0,
      frequency: new Array(256).fill(0),
      waveform: new Array(1024).fill(128),
      peak: 0,
      rms: 0
    };
  }
}

/**
 * 3D音频节点
 */
export class Audio3DNode extends VisualScriptNode {
  constructor() {
    super('Audio3D', '3D音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioClip', 'string', '音频片段');
    this.addInput('position', 'vector3', '位置');
    this.addInput('velocity', 'vector3', '速度');
    this.addInput('orientation', 'vector3', '方向');
    this.addInput('volume', 'number', '音量', 1.0);
    this.addInput('loop', 'boolean', '循环', false);
    this.addInput('refDistance', 'number', '参考距离', 1.0);
    this.addInput('maxDistance', 'number', '最大距离', 10000);
    this.addInput('rolloffFactor', 'number', '衰减因子', 1.0);
    this.addInput('coneInnerAngle', 'number', '内锥角', 360);
    this.addInput('coneOuterAngle', 'number', '外锥角', 360);
    this.addInput('coneOuterGain', 'number', '外锥增益', 0);
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
    this.addOutput('audioId', 'string', '音频ID');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioClip || !inputs.position) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const audioId = `audio3d_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      const options = {
        type: AudioType.SOUND,
        volume: Math.max(0, Math.min(1, inputs.volume || 1.0)),
        loop: inputs.loop || false,
        position: inputs.position,
        velocity: inputs.velocity,
        orientation: inputs.orientation,
        refDistance: inputs.refDistance || 1.0,
        maxDistance: inputs.maxDistance || 10000,
        rolloffFactor: inputs.rolloffFactor || 1.0,
        coneInnerAngle: inputs.coneInnerAngle || 360,
        coneOuterAngle: inputs.coneOuterAngle || 360,
        coneOuterGain: inputs.coneOuterGain || 0
      };

      const success = audioSystem.play(audioId, inputs.audioClip, options);

      if (success) {
        return {
          completed: true,
          audioId: audioId
        };
      } else {
        return { failed: true };
      }
    } catch (error) {
      console.error('播放3D音频失败:', error);
      return { failed: true };
    }
  }
}

/**
 * 暂停音频节点
 */
export class PauseAudioNode extends VisualScriptNode {
  constructor() {
    super('PauseAudio', '暂停音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioId', 'string', '音频ID');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioId) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const success = audioSystem.pause(inputs.audioId);
      return success ? { completed: true } : { failed: true };
    } catch (error) {
      console.error('暂停音频失败:', error);
      return { failed: true };
    }
  }
}

/**
 * 恢复音频节点
 */
export class ResumeAudioNode extends VisualScriptNode {
  constructor() {
    super('ResumeAudio', '恢复音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioId', 'string', '音频ID');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioId) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const success = audioSystem.resume(inputs.audioId);
      return success ? { completed: true } : { failed: true };
    } catch (error) {
      console.error('恢复音频失败:', error);
      return { failed: true };
    }
  }
}

/**
 * 音频淡入淡出节点
 */
export class AudioFadeNode extends VisualScriptNode {
  constructor() {
    super('AudioFade', '音频淡入淡出');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('targetVolume', 'number', '目标音量', 1.0);
    this.addInput('duration', 'number', '持续时间', 1.0);
    this.addInput('fadeType', 'string', '淡入淡出类型', 'linear');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioId) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const source = audioSystem.getSource(inputs.audioId);
      if (!source) {
        return { failed: true };
      }

      const targetVolume = Math.max(0, Math.min(1, inputs.targetVolume || 1.0));
      const duration = Math.max(0, inputs.duration || 1.0);

      // 实现音频淡入淡出
      this.fadeAudio(source, targetVolume, duration, inputs.fadeType);

      return { completed: true };
    } catch (error) {
      console.error('音频淡入淡出失败:', error);
      return { failed: true };
    }
  }

  private fadeAudio(source: any, targetVolume: number, duration: number, fadeType: string): void {
    // 简化的淡入淡出实现
    const currentVolume = source.getVolume?.() || 1.0;
    const volumeDiff = targetVolume - currentVolume;
    const steps = Math.max(1, Math.floor(duration * 60)); // 假设60FPS
    const stepSize = volumeDiff / steps;

    let currentStep = 0;
    const fadeInterval = setInterval(() => {
      currentStep++;
      let newVolume: number;

      switch (fadeType) {
        case 'exponential':
          newVolume = currentVolume + volumeDiff * (1 - Math.exp(-currentStep / steps * 3));
          break;
        case 'logarithmic':
          newVolume = currentVolume + volumeDiff * Math.log(currentStep + 1) / Math.log(steps + 1);
          break;
        default: // linear
          newVolume = currentVolume + stepSize * currentStep;
      }

      source.setVolume?.(Math.max(0, Math.min(1, newVolume)));

      if (currentStep >= steps) {
        source.setVolume?.(targetVolume);
        clearInterval(fadeInterval);
      }
    }, duration * 1000 / steps);
  }
}

/**
 * 音频滤波器节点
 */
export class AudioFilterNode extends VisualScriptNode {
  constructor() {
    super('AudioFilter', '音频滤波器');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('filterType', 'string', '滤波器类型', 'lowpass');
    this.addInput('frequency', 'number', '频率', 1000);
    this.addInput('Q', 'number', 'Q值', 1);
    this.addInput('gain', 'number', '增益', 0);
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioId) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const source = audioSystem.getSource(inputs.audioId);
      if (!source) {
        return { failed: true };
      }

      // 应用音频滤波器
      this.applyFilter(source, inputs);

      return { completed: true };
    } catch (error) {
      console.error('应用音频滤波器失败:', error);
      return { failed: true };
    }
  }

  private applyFilter(source: any, inputs: any): void {
    // 这里需要实际的滤波器实现
    // 由于AudioSource的具体实现可能不同，这里提供一个通用接口
    if (source.applyFilter) {
      source.applyFilter({
        type: inputs.filterType || 'lowpass',
        frequency: inputs.frequency || 1000,
        Q: inputs.Q || 1,
        gain: inputs.gain || 0
      });
    }
  }
}

/**
 * 音频录制节点
 */
export class AudioRecordNode extends VisualScriptNode {
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];

  constructor() {
    super('AudioRecord', '音频录制');
    this.addInput('startRecord', 'exec', '开始录制');
    this.addInput('stopRecord', 'exec', '停止录制');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('sampleRate', 'number', '采样率', 44100);
    this.addInput('channels', 'number', '声道数', 1);
    this.addOutput('recording', 'exec', '录制中');
    this.addOutput('stopped', 'exec', '已停止');
    this.addOutput('audioData', 'blob', '音频数据');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (inputs.startRecord) {
      return this.startRecording(inputs);
    } else if (inputs.stopRecord) {
      return this.stopRecording();
    }
    return {};
  }

  private async startRecording(inputs: any): Promise<any> {
    try {
      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: inputs.deviceId ? { exact: inputs.deviceId } : undefined,
          sampleRate: inputs.sampleRate || 44100,
          channelCount: inputs.channels || 1
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      this.recordedChunks = [];
      this.mediaRecorder = new MediaRecorder(stream);

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        // 录制停止时的处理逻辑
        // 音频数据将在stopRecording方法中处理
      };

      this.mediaRecorder.start();
      return { recording: true };
    } catch (error) {
      console.error('开始录制失败:', error);
      return { failed: true };
    }
  }

  private stopRecording(): any {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();

      // 停止所有音频轨道
      this.mediaRecorder.stream.getAudioTracks().forEach(track => track.stop());

      const audioBlob = new Blob(this.recordedChunks, { type: 'audio/wav' });
      return {
        stopped: true,
        audioData: audioBlob
      };
    }
    return { failed: true };
  }
}

/**
 * 音频可视化节点
 */
export class AudioVisualizerNode extends VisualScriptNode {
  constructor() {
    super('AudioVisualizer', '音频可视化');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('visualType', 'string', '可视化类型', 'spectrum');
    this.addInput('canvasElement', 'object', '画布元素');
    this.addInput('width', 'number', '宽度', 800);
    this.addInput('height', 'number', '高度', 200);
    this.addInput('color', 'string', '颜色', '#00ff00');
    this.addOutput('updated', 'exec', '已更新');
    this.addOutput('failed', 'exec', '失败');
  }

  public execute(inputs: any): any {
    if (!inputs.audioId || !inputs.canvasElement) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      return { failed: true };
    }

    try {
      const source = audioSystem.getSource(inputs.audioId);
      if (!source) {
        return { failed: true };
      }

      // 绘制音频可视化
      this.drawVisualization(inputs, source);

      return { updated: true };
    } catch (error) {
      console.error('音频可视化失败:', error);
      return { failed: true };
    }
  }

  private drawVisualization(inputs: any, source: any): void {
    const canvas = inputs.canvasElement;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = inputs.width || 800;
    const height = inputs.height || 200;
    const color = inputs.color || '#00ff00';

    // 清除画布
    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = color;

    // 获取音频分析数据
    const analyser = source.getAnalyser?.();
    if (!analyser) return;

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    switch (inputs.visualType) {
      case 'spectrum':
        analyser.getByteFrequencyData(dataArray);
        this.drawSpectrum(ctx, dataArray, width, height);
        break;
      case 'waveform':
        analyser.getByteTimeDomainData(dataArray);
        this.drawWaveform(ctx, dataArray, width, height);
        break;
      case 'bars':
        analyser.getByteFrequencyData(dataArray);
        this.drawBars(ctx, dataArray, width, height);
        break;
    }
  }

  private drawSpectrum(ctx: CanvasRenderingContext2D, dataArray: Uint8Array, width: number, height: number): void {
    const barWidth = width / dataArray.length;

    for (let i = 0; i < dataArray.length; i++) {
      const barHeight = (dataArray[i] / 255) * height;
      ctx.fillRect(i * barWidth, height - barHeight, barWidth, barHeight);
    }
  }

  private drawWaveform(ctx: CanvasRenderingContext2D, dataArray: Uint8Array, width: number, height: number): void {
    ctx.beginPath();
    ctx.strokeStyle = ctx.fillStyle;
    ctx.lineWidth = 2;

    const sliceWidth = width / dataArray.length;
    let x = 0;

    for (let i = 0; i < dataArray.length; i++) {
      const v = dataArray[i] / 128.0;
      const y = v * height / 2;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }

      x += sliceWidth;
    }

    ctx.stroke();
  }

  private drawBars(ctx: CanvasRenderingContext2D, dataArray: Uint8Array, width: number, height: number): void {
    const barCount = Math.min(64, dataArray.length);
    const barWidth = width / barCount;
    const step = Math.floor(dataArray.length / barCount);

    for (let i = 0; i < barCount; i++) {
      const barHeight = (dataArray[i * step] / 255) * height;
      ctx.fillRect(i * barWidth, height - barHeight, barWidth - 1, barHeight);
    }
  }
}

/**
 * 音频混合器节点
 */
export class AudioMixerNode extends VisualScriptNode {
  constructor() {
    super('AudioMixer', '音频混合器');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioIds', 'array', '音频ID列表');
    this.addInput('volumes', 'array', '音量列表');
    this.addInput('outputId', 'string', '输出音频ID');
    this.addOutput('completed', 'exec', '完成');
    this.addOutput('failed', 'exec', '失败');
    this.addOutput('mixedAudioId', 'string', '混合音频ID');
  }

  public execute(inputs: any): any {
    if (!inputs.trigger || !inputs.audioIds || !Array.isArray(inputs.audioIds)) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      console.warn('音频系统未初始化');
      return { failed: true };
    }

    try {
      const audioIds = inputs.audioIds;
      const volumes = inputs.volumes || audioIds.map(() => 1.0);
      const outputId = inputs.outputId || `mixed_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // 实现音频混合逻辑
      const success = this.mixAudioSources(audioSystem, audioIds, volumes, outputId);

      if (success) {
        return {
          completed: true,
          mixedAudioId: outputId
        };
      } else {
        return { failed: true };
      }
    } catch (error) {
      console.error('音频混合失败:', error);
      return { failed: true };
    }
  }

  private mixAudioSources(audioSystem: AudioSystem, audioIds: string[], volumes: number[], outputId: string): boolean {
    // 简化的音频混合实现
    // 实际实现需要更复杂的音频处理
    try {
      const sources = audioIds.map(id => audioSystem.getSource(id)).filter(Boolean);
      if (sources.length === 0) return false;

      // 创建混合输出源
      const mixedSource = audioSystem.createSource(outputId);
      if (!mixedSource) return false;

      // 这里需要实际的音频混合算法
      // 暂时只是设置第一个源作为输出
      if (sources.length > 0) {
        const firstSource = sources[0];
        const volume = volumes[0] || 1.0;
        firstSource.setVolume(volume);
      }

      return true;
    } catch (error) {
      console.error('混合音频源失败:', error);
      return false;
    }
  }
}

/**
 * 音频事件监听节点
 */
export class AudioEventListenerNode extends VisualScriptNode {
  constructor() {
    super('AudioEventListener', '音频事件监听');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('eventType', 'string', '事件类型', 'ended');
    this.addOutput('onPlay', 'exec', '播放时');
    this.addOutput('onPause', 'exec', '暂停时');
    this.addOutput('onStop', 'exec', '停止时');
    this.addOutput('onEnded', 'exec', '结束时');
    this.addOutput('onError', 'exec', '错误时');
    this.addOutput('eventData', 'object', '事件数据');
  }

  public execute(inputs: any): any {
    if (!inputs.audioId) {
      return {};
    }

    const audioSystem = getAudioSystem();
    if (!audioSystem) {
      return {};
    }

    try {
      const source = audioSystem.getSource(inputs.audioId);
      if (!source) {
        return {};
      }

      // 监听音频事件
      this.setupEventListeners(source, inputs.eventType);

      return {};
    } catch (error) {
      console.error('设置音频事件监听失败:', error);
      return {};
    }
  }

  private setupEventListeners(source: any, eventType: string): void {
    // 这里需要根据AudioSource的实际实现来设置事件监听
    if (source.addEventListener) {
      source.addEventListener('play', () => {
        // 触发播放事件输出
      });

      source.addEventListener('pause', () => {
        // 触发暂停事件输出
      });

      source.addEventListener('ended', () => {
        // 触发结束事件输出
      });

      source.addEventListener('error', (error: any) => {
        // 触发错误事件输出
      });
    }
  }
}

/**
 * 注册音频节点
 */
export function registerAudioNodes(): void {
  NodeRegistry.register('PlayAudio', PlayAudioNode);
  NodeRegistry.register('StopAudio', StopAudioNode);
  NodeRegistry.register('PauseAudio', PauseAudioNode);
  NodeRegistry.register('ResumeAudio', ResumeAudioNode);
  NodeRegistry.register('SetVolume', SetVolumeNode);
  NodeRegistry.register('AudioAnalyzer', AudioAnalyzerNode);
  NodeRegistry.register('Audio3D', Audio3DNode);
  NodeRegistry.register('AudioFade', AudioFadeNode);
  NodeRegistry.register('AudioFilter', AudioFilterNode);
  NodeRegistry.register('AudioRecord', AudioRecordNode);
  NodeRegistry.register('AudioVisualizer', AudioVisualizerNode);
  NodeRegistry.register('AudioMixer', AudioMixerNode);
  NodeRegistry.register('AudioEventListener', AudioEventListenerNode);
}
